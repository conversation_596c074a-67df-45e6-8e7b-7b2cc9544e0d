"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
// Ensure .env variables are loaded before anything else
require("dotenv/config");
/**
 * Main Application Entry Point
 * Refactored main entry point using the new modular architecture
 */
const application_1 = require("./core/application");
const events_1 = require("./events");
const EconomyService_1 = require("./services/economy/EconomyService");
const RoleService_1 = require("./services/role/RoleService");
const logger_1 = require("./core/logger");
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
/**
 * Main application startup function
 */
async function main() {
    // Prevent the bot from doing anything for the first 10 seconds after startup
    // This helps ensure all services (especially DB) are ready before handling commands/events
    const startupLogger = (0, logger_1.getLogger)();
    startupLogger.info('[Main] Waiting 10 seconds before allowing any bot actions (startup delay)...');
    await new Promise(resolve => setTimeout(resolve, 10000));
    startupLogger.info('[Main] Startup delay complete. Continuing with initialization.');
    // Print all environment variables for debugging (especially on Discloud)
    // Remove this in production!
    // eslint-disable-next-line no-console
    console.log('[DEBUG] process.env:', process.env);
    // Check for required environment variables
    const requiredEnv = ['BOT_TOKEN', 'MONGODB_URI'];
    const missingEnv = requiredEnv.filter((key) => !process.env[key]);
    if (missingEnv.length > 0) {
        // eslint-disable-next-line no-console
        console.error('[ENV][Missing] The following required environment variables are missing:', missingEnv);
        throw new Error(`Missing required environment variables: ${missingEnv.join(', ')}`);
    }
    const logger = (0, logger_1.getLogger)();
    try {
        logger.info('[Main] Starting Economy Bot with refactored architecture...');
        // Initialize the application
        const app = await (0, application_1.startApplication)();
        // Register additional services
        await registerServices(app);
        // Load and register commands
        await loadCommands(app);
        // Initialize event handlers
        const eventManager = new events_1.EventManager(app);
        eventManager.initialize();
        logger.info('[Main] Economy Bot started successfully!');
        logger.info(`[Main] Bot is ready as ${app.client.user?.tag}`);
    }
    catch (error) {
        // Print any startup error directly to the console for visibility
        // eslint-disable-next-line no-console
        console.error('[MAIN][Raw Error]', error);
        logger.error('[Main] Failed to start application', { error });
        process.exit(1);
    }
}
/**
 * Register additional services
 */
async function registerServices(app) {
    const logger = (0, logger_1.getLogger)();
    try {
        // Register economy service
        const economyService = new EconomyService_1.EconomyService(app);
        app.registerService(economyService, {
            autoStart: true,
            dependencies: ['DatabaseService'],
            priority: 2
        });
        // Register role service
        const roleService = new RoleService_1.RoleService(app);
        app.registerService(roleService, {
            autoStart: true,
            dependencies: ['DatabaseService', 'EconomyService'],
            priority: 3
        });
        logger.info('[Main] Additional services registered');
    }
    catch (error) {
        logger.error('[Main] Failed to register services', { error });
        throw error;
    }
}
/**
 * Load and register commands
 */
async function loadCommands(app) {
    const logger = (0, logger_1.getLogger)();
    try {
        // Initialize commands collection if it doesn't exist
        if (!app.client.commands) {
            app.client.commands = new Map();
        }
        let loadedCommands = 0;
        // Load legacy commands (individual files)
        const commandsPath = path_1.default.join(__dirname, 'commands');
        const commandFiles = fs_1.default.readdirSync(commandsPath).filter(file => (file.endsWith('.js') || file.endsWith('.ts')) &&
            !file.endsWith('.d.ts') &&
            !file.includes('index') &&
            !file.includes('Manager') &&
            !file.includes('Base'));
        // Skip files that are handled by new architecture
        const skipFiles = new Set([
            'balance.js', 'balance.ts',
            'pay.js', 'pay.ts',
            'give.js', 'give.ts',
            'enhancerole.js', 'enhancerole.ts',
            'updatenames.js', 'updatenames.ts'
        ]);
        for (const file of commandFiles) {
            if (skipFiles.has(file)) {
                logger.debug(`[Main] Skipping ${file} (handled by new architecture)`);
                continue;
            }
            try {
                const filePath = path_1.default.join(commandsPath, file);
                const command = require(filePath);
                if (command.data && command.execute) {
                    app.client.commands.set(command.data.name, command);
                    loadedCommands++;
                    logger.debug(`[Main] Loaded legacy command: ${command.data.name}`);
                }
                else {
                    logger.warn(`[Main] Invalid command file: ${file}`);
                }
            }
            catch (error) {
                logger.error(`[Main] Failed to load command file: ${file}`, { error });
            }
        }
        // Load new architecture commands
        try {
            const { commandManager } = require('./commands/CommandManager');
            const stats = await commandManager.loadCommands();
            const newCommands = commandManager.getDiscordCommands();
            for (const [name, command] of newCommands) {
                app.client.commands.set(name, command);
                loadedCommands++;
                logger.debug(`[Main] Loaded new architecture command: ${name}`);
            }
            logger.info(`[Main] CommandManager loaded ${stats.newArchitecture} new architecture commands`);
        }
        catch (error) {
            logger.error('[Main] Failed to load new architecture commands', { error });
        }
        logger.info(`[Main] Loaded ${loadedCommands} commands`);
    }
    catch (error) {
        logger.error('[Main] Failed to load commands', { error });
        throw error;
    }
}
/**
 * Handle uncaught exceptions and rejections
 */
process.on('uncaughtException', (error) => {
    const logger = (0, logger_1.getLogger)();
    logger.error('[Main] Uncaught exception', { error });
    process.exit(1);
});
process.on('unhandledRejection', (reason, promise) => {
    const logger = (0, logger_1.getLogger)();
    logger.error('[Main] Unhandled rejection', { reason, promise });
});
// Start the application
if (require.main === module) {
    main().catch((error) => {
        // Print any error directly to the console for maximum visibility
        // eslint-disable-next-line no-console
        console.error('[MAIN][Startup Error]', error);
        if (error && error.stack) {
            // eslint-disable-next-line no-console
            console.error('[MAIN][Startup Error Stack]', error.stack);
        }
        process.exit(1);
    });
}
exports.default = main;
//# sourceMappingURL=main.js.map