{"version": 3, "file": "deploy-role-commands.js", "sourceRoot": "", "sources": ["../src/deploy-role-commands.ts"], "names": [], "mappings": ";;;;;AAAA,2CAAoF;AACpF,oDAA4B;AAE5B,gBAAM,CAAC,MAAM,EAAE,CAAC;AAEhB,KAAK,UAAU,kBAAkB;IAC/B,IAAI,CAAC;QACH,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;QAEpD,0DAA0D;QAC1D,MAAM,kBAAkB,GAAG,IAAI,gCAAmB,EAAE;aACjD,OAAO,CAAC,aAAa,CAAC;aACtB,cAAc,CAAC,oDAAoD,CAAC;aACpE,aAAa,CAAC,MAAM,CAAC,EAAE,CACtB,MAAM;aACH,OAAO,CAAC,MAAM,CAAC;aACf,cAAc,CAAC,wBAAwB,CAAC;aACxC,WAAW,CAAC,IAAI,CAAC,CACrB;aACA,eAAe,CAAC,MAAM,CAAC,EAAE,CACxB,MAAM;aACH,OAAO,CAAC,QAAQ,CAAC;aACjB,cAAc,CAAC,kDAAkD,CAAC;aAClE,WAAW,CAAC,IAAI,CAAC;aACjB,YAAY,CAAC,EAAE,CAAC,CACpB;aACA,2BAA2B,CAAC,gCAAmB,CAAC,eAAe,CAAC,CAAC;QAEpE,MAAM,kBAAkB,GAAG,IAAI,gCAAmB,EAAE;aACjD,OAAO,CAAC,aAAa,CAAC;aACtB,cAAc,CAAC,uEAAuE,CAAC;aACvF,2BAA2B,CAAC,gCAAmB,CAAC,eAAe,CAAC,CAAC;QAEpE,MAAM,QAAQ,GAAG;YACf,kBAAkB,CAAC,MAAM,EAAE;YAC3B,kBAAkB,CAAC,MAAM,EAAE;SAC5B,CAAC;QAEF,OAAO,CAAC,GAAG,CAAC,0BAA0B,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;QACzD,OAAO,CAAC,GAAG,CAAC,mDAAmD,CAAC,CAAC;QACjE,OAAO,CAAC,GAAG,CAAC,8CAA8C,CAAC,CAAC;QAE5D,oBAAoB;QACpB,MAAM,IAAI,GAAG,IAAI,iBAAI,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,SAAU,CAAC,CAAC;QAE1E,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;QAEtD,8BAA8B;QAC9B,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,GAAG,CACrC,mBAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,GAAG,CAAC,SAAU,CAAC,CAC1C,CAAC;QAEX,OAAO,CAAC,GAAG,CAAC,YAAY,gBAAgB,CAAC,MAAM,oBAAoB,CAAC,CAAC;QAErE,wCAAwC;QACxC,MAAM,WAAW,GAAG,CAAC,GAAG,gBAAgB,CAAC,CAAC;QAE1C,+CAA+C;QAC/C,MAAM,gBAAgB,GAAG,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC;QACxD,MAAM,gBAAgB,GAAG,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,gBAAgB,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;QAEzF,uBAAuB;QACvB,MAAM,aAAa,GAAG,CAAC,GAAG,gBAAgB,EAAE,GAAG,QAAQ,CAAC,CAAC;QAEzD,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,GAAG,CACzB,mBAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,GAAG,CAAC,SAAU,CAAC,EAClD,EAAE,IAAI,EAAE,aAAa,EAAE,CACf,CAAC;QAEX,OAAO,CAAC,GAAG,CAAC,2BAA2B,IAAI,CAAC,MAAM,kBAAkB,CAAC,CAAC;QAEtE,wBAAwB;QACxB,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,gBAAgB,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;QAC5E,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;QACvD,KAAK,MAAM,GAAG,IAAI,WAAW,EAAE,CAAC;YAC9B,OAAO,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,IAAI,MAAM,GAAG,CAAC,WAAW,EAAE,CAAC,CAAC;QACxD,CAAC;IAEH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;QACzD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;AACH,CAAC;AAED,iBAAiB;AACjB,kBAAkB,EAAE,CAAC"}