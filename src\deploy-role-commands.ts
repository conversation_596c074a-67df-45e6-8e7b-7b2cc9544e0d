import { REST, Routes, Slash<PERSON>ommandBuilder, PermissionFlagsBits } from 'discord.js';
import dotenv from 'dotenv';

dotenv.config();

async function deployRoleCommands() {
  try {
    console.log('🔄 Deploying role prefix commands...');

    // Create the commands manually to avoid dependency issues
    const enhanceRoleCommand = new SlashCommandBuilder()
      .setName('enhancerole')
      .setDescription('Assign a prefix to all users with a specified role')
      .addRoleOption(option =>
        option
          .setName('role')
          .setDescription('Discord role to target')
          .setRequired(true)
      )
      .addStringOption(option =>
        option
          .setName('prefix')
          .setDescription('Text/emoji prefix to prepend (max 10 characters)')
          .setRequired(true)
          .setMaxLength(10)
      )
      .setDefaultMemberPermissions(PermissionFlagsBits.ManageNicknames);

    const updateNamesCommand = new SlashCommandBuilder()
      .setName('updatenames')
      .setDescription('Ensure all members have correct prefixes based on their current roles')
      .setDefaultMemberPermissions(PermissionFlagsBits.ManageNicknames);

    const commands = [
      enhanceRoleCommand.toJSON(),
      updateNamesCommand.toJSON()
    ];

    console.log(`📊 Commands to deploy: ${commands.length}`);
    console.log('   • /enhancerole - Assign prefix to role members');
    console.log('   • /updatenames - Sync all member prefixes');

    // Deploy to Discord
    const rest = new REST({ version: '10' }).setToken(process.env.BOT_TOKEN!);

    console.log('🚀 Started refreshing role commands...');
    
    // Get existing commands first
    const existingCommands = await rest.get(
      Routes.applicationCommands(process.env.CLIENT_ID!)
    ) as any[];

    console.log(`📋 Found ${existingCommands.length} existing commands`);

    // Add our new commands to existing ones
    const allCommands = [...existingCommands];
    
    // Remove any existing versions of our commands
    const commandsToRemove = ['enhancerole', 'updatenames'];
    const filteredCommands = allCommands.filter(cmd => !commandsToRemove.includes(cmd.name));
    
    // Add our new commands
    const finalCommands = [...filteredCommands, ...commands];

    const data = await rest.put(
      Routes.applicationCommands(process.env.CLIENT_ID!),
      { body: finalCommands }
    ) as any[];

    console.log(`✅ Successfully deployed ${data.length} total commands.`);
    
    // Show our new commands
    const newCommands = data.filter(cmd => commandsToRemove.includes(cmd.name));
    console.log('\n🎉 New role prefix commands deployed:');
    for (const cmd of newCommands) {
      console.log(`   • /${cmd.name} - ${cmd.description}`);
    }

  } catch (error) {
    console.error('❌ Error deploying role commands:', error);
    process.exit(1);
  }
}

// Run deployment
deployRoleCommands();
